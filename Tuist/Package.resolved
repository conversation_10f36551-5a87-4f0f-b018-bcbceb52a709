{"originHash": "286647f37d686439294981ba98ba6a5d5769d74810a730e3971325d6c5661e92", "pins": [{"identity": "clerk-ios", "kind": "remoteSourceControl", "location": "https://github.com/blueragesoftware/clerk-ios", "state": {"branch": "main", "revision": "1d07f7e352faad17f9728359bdee1976295fd7d7"}}, {"identity": "convex-swift", "kind": "remoteSourceControl", "location": "https://github.com/blueragesoftware/convex-swift", "state": {"branch": "main", "revision": "b73cc874f06b637bc47b05f71e5bdafc1a7f07b3"}}, {"identity": "factory", "kind": "remoteSourceControl", "location": "https://github.com/hmlongco/Factory", "state": {"revision": "ccc898f21992ebc130bc04cc197460a5ae230bcf", "version": "2.5.3"}}, {"identity": "get", "kind": "remoteSourceControl", "location": "https://github.com/kean/Get", "state": {"revision": "31249885da1052872e0ac91a2943f62567c0d96d", "version": "2.2.1"}}, {"identity": "kingfisher", "kind": "remoteSourceControl", "location": "https://github.com/onevcat/Kingfisher.git", "state": {"revision": "2015fda791daa72c8058619545a593bf8c1dd59f", "version": "8.5.0"}}, {"identity": "navigator", "kind": "remoteSourceControl", "location": "https://github.com/hmlongco/Navigator", "state": {"revision": "cdf1194c3ff07bc74c45aeaa623c462699e408d7", "version": "1.1.1"}}, {"identity": "networkimage", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/NetworkImage", "state": {"revision": "2849f5323265386e200484b0d0f896e73c3411b9", "version": "6.0.1"}}, {"identity": "nuke", "kind": "remoteSourceControl", "location": "https://github.com/kean/Nuke", "state": {"revision": "0ead44350d2737db384908569c012fe67c421e4d", "version": "12.8.0"}}, {"identity": "phonenumberkit", "kind": "remoteSourceControl", "location": "https://github.com/marmelroy/PhoneNumberKit", "state": {"revision": "8cba2258060e356d5fc4836d4ff8549faa2409dd", "version": "4.1.4"}}, {"identity": "pinlayout", "kind": "remoteSourceControl", "location": "https://github.com/layoutBox/PinLayout", "state": {"revision": "72f5300042618340df21500f6c4ce71bb78b93e1", "version": "1.10.6"}}, {"identity": "posthog-ios", "kind": "remoteSourceControl", "location": "https://github.com/PostHog/posthog-ios", "state": {"revision": "f381818bc153f63fbe13d6822e65cc7233ba4647", "version": "3.31.0"}}, {"identity": "sentry-cocoa", "kind": "remoteSourceControl", "location": "https://github.com/getsentry/sentry-cocoa", "state": {"revision": "2c70925b98238b7efacde11d30075328c3cb6a2a", "version": "8.55.1"}}, {"identity": "simplekeychain", "kind": "remoteSourceControl", "location": "https://github.com/auth0/SimpleKeychain", "state": {"revision": "776c4a6db74d5c6c143974be91c383680d468630", "version": "1.3.0"}}, {"identity": "svgview", "kind": "remoteSourceControl", "location": "https://github.com/exyte/SVGView", "state": {"revision": "6465962facdd25cb96eaebc35603afa2f15d2c0d", "version": "1.0.6"}}, {"identity": "swift-algorithms", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-algorithms", "state": {"revision": "87e50f483c54e6efd60e885f7f5aa946cee68023", "version": "1.2.1"}}, {"identity": "swift-cmark", "kind": "remoteSourceControl", "location": "https://github.com/swiftlang/swift-cmark", "state": {"revision": "b022b08312decdc46585e0b3440d97f6f22ef703", "version": "0.6.0"}}, {"identity": "swift-markdown-ui", "kind": "remoteSourceControl", "location": "https://github.com/gonzalezreal/swift-markdown-ui.git", "state": {"revision": "5f613358148239d0292c0cef674a3c2314737f9e", "version": "2.4.1"}}, {"identity": "swift-numerics", "kind": "remoteSourceControl", "location": "https://github.com/apple/swift-numerics.git", "state": {"revision": "bbadd4b853a33fd78c4ae977d17bb2af15eb3f2a", "version": "1.1.0"}}, {"identity": "swiftui-introspect", "kind": "remoteSourceControl", "location": "https://github.com/siteline/swiftui-introspect", "state": {"revision": "a08b87f96b41055577721a6e397562b21ad52454", "version": "26.0.0"}}, {"identity": "swiftui-shimmer", "kind": "remoteSourceControl", "location": "https://github.com/markiv/SwiftUI-Shimmer", "state": {"revision": "0226e21f9bf355d40e07e5f5e1c33679d50e167f", "version": "1.5.1"}}], "version": 3}
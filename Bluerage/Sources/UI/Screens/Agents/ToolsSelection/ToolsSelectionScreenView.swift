import SwiftUI
import Navigator<PERSON>
import OSLog
import PostHog

struct ToolsSelectionScreenView: View {

    @State private var viewModel: ToolsSelectionScreenViewModel

    @Environment(\.dismiss) private var dismiss

    private let onToolSelected: (Tool) -> Void

    init(agentToolsSlugSet: Set<String>, onToolSelected: @escaping (Tool) -> Void) {
        self.viewModel = ToolsSelectionScreenViewModel(agentToolsSlugSet: agentToolsSlugSet)
        self.onToolSelected = onToolSelected
    }

    var body: some View {
        ManagedNavigationStack { navigator in
            self.content(with: navigator)
                .navigationTitle(BluerageStrings.toolsSelectionNavigationTitle)
                .navigationBarTitleDisplayMode(.inline)
                .navigationDestination(ToolsSelectionDestinations.self)
        }
        .onAppear {
            self.viewModel.load()
        }
        .onReceive(NotificationCenter.default.publisher(for: UIApplication.willEnterForegroundNotification)) { _ in
            self.viewModel.load()
        }
        .presentationDetents([.medium, .large])
        .presentationDragIndicator(.visible)
        .errorAlert(error: self.viewModel.state.alertError) {
            self.viewModel.resetAlertError()
        }
        .postHogScreenView("ToolsSelectionScreenView")
    }

    @ViewBuilder
    private func content(with navigator: Navigator) -> some View {
        switch self.viewModel.state.main {
        case .loading:
            ProgressView()
        case .loaded(let activeTools, let inactiveTools):
            ToolsSelectionLoadedView(
                activeTools: activeTools,
                inactiveTools: inactiveTools,
                onActiveToolSelected: { activeTool in
                    self.onToolSelected(activeTool)
                    self.dismiss()
                }, onInactiveToolSelected: { inactiveTool in
                    Task {
                        do {
                            let redirectUrl = try await self.viewModel.connectTool(with: inactiveTool.authConfigId)

                            navigator.navigate(to: ToolsSelectionDestinations.authWebView(
                                url: redirectUrl,
                                callback: Callback { [weak viewModel] _ in
                                    viewModel?.load()
                                }
                            ))
                        } catch {
                            Logger.tools.error("Error connecting tool: \(error.localizedDescription, privacy: .public)")

                            self.viewModel.showErrorAlert(with: error)
                        }
                    }
                }
            )
        case .empty:
            PlaceholderView(imageName: BluerageAsset.Assets.toolsPlaceholder100.name,
                            title: BluerageStrings.toolsSelectionEmptyPlaceholderTitle,
                            description: BluerageStrings.toolsSelectionEmptyPlaceholderDescription)
        case .error:
            PlaceholderView.error(title: BluerageStrings.toolsSelectionErrorPlaceholderTitle) {
                self.viewModel.load()
            }
        case .allToolsUsed:
            PlaceholderView(imageName: BluerageAsset.Assets.toolsPlaceholder100.name,
                            title: BluerageStrings.toolsSelectionAllToolsConnectedPlaceholderTitle,
                            description: BluerageStrings.toolsSelectionAllToolsConnectedPlaceholderPlaceholder)
        }
    }
}

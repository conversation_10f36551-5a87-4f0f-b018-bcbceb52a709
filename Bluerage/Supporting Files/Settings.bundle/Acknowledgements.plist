<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<key>PreferenceSpecifiers</key>
	<array>
		<dict>
			<key>Title</key>
			<string>Licenses</string>
			<key>Type</key>
			<string>PSGroupSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/Clerk</string>
			<key>Title</key>
			<string>Clerk</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/ConvexMobile</string>
			<key>Title</key>
			<string>ConvexMobile</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/Factory</string>
			<key>Title</key>
			<string>Factory</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/Get</string>
			<key>Title</key>
			<string>Get</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/Kingfisher</string>
			<key>Title</key>
			<string>Kingfisher</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/NavigatorUI</string>
			<key>Title</key>
			<string>NavigatorUI</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/NetworkImage</string>
			<key>Title</key>
			<string>NetworkImage</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/Nuke</string>
			<key>Title</key>
			<string>Nuke</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/PhoneNumberKit</string>
			<key>Title</key>
			<string>PhoneNumberKit</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/PinLayout</string>
			<key>Title</key>
			<string>PinLayout</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/PostHog</string>
			<key>Title</key>
			<string>PostHog</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/Sentry</string>
			<key>Title</key>
			<string>Sentry</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/SimpleKeychain</string>
			<key>Title</key>
			<string>SimpleKeychain</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/SVGView</string>
			<key>Title</key>
			<string>SVGView</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/swift-algorithms</string>
			<key>Title</key>
			<string>swift-algorithms</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/cmark-gfm</string>
			<key>Title</key>
			<string>cmark-gfm</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/swift-markdown-ui</string>
			<key>Title</key>
			<string>swift-markdown-ui</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/swift-numerics</string>
			<key>Title</key>
			<string>swift-numerics</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/swiftui-introspect</string>
			<key>Title</key>
			<string>swiftui-introspect</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
		<dict>
			<key>File</key>
			<string>Packages/SwiftUI-Shimmer</string>
			<key>Title</key>
			<string>SwiftUI-Shimmer</string>
			<key>Type</key>
			<string>PSChildPaneSpecifier</string>
		</dict>
	</array>
	<key>StringsTable</key>
	<string>Acknowledgements</string>
</dict>
</plist>

name: Generate Licenses

on:
  pull_request:
    branches: [main]
  push:
    branches: [main]

jobs:
  generate-licenses:
    name: Generate and Check Licenses
    runs-on: macos-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Install swift-package-list
        run: |
          brew tap FelixHerrmann/tap
          brew install swift-package-list

      - name: Generate Licenses
        run: make generate-licenses

      - name: Check for License Changes
        run: |
          if [[ -n $(git status --porcelain) ]]; then
            echo "Error: License generation created changes. Please run 'make generate-licenses' locally and commit the changes."
            git status
            git diff
            exit 1
          fi

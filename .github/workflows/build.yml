name: Build Application

on:
  pull_request:
    branches: [ main ]
  push:
    branches: [ main ]

jobs:
  build:
    runs-on: macos-latest
    
    steps:
      - name: Create xcconfigs
        run: |
          cp xcconfigs/Config.example.xcconfig xcconfigs/Release.xcconfig
          cp xcconfigs/Config.example.xcconfig xcconfigs/Debug.xcconfig
      
      - uses: actions/checkout@v3
      - uses: jdx/mise-action@v2  
          
      - name: Tuist Generate
          run: tuist generate
          env:
            TUIST_BUNDLE_ID: com.bluerage.ci
            TUIST_SENTRY_ORG: bluerage-ci
        
      - name: Tuist Install
          run: tuist install

      - name: Tuist Build
          run: tuist build
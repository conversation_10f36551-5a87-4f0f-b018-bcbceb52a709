// Use Tuist
ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS=NO

DEBUG_INFORMATION_FORMAT=dwarf-with-dsym
DEBUG_INFORMATION_FORMAT[config=Debug]=dwarf

DEFINES_MODULE=YES

ENABLE_USER_SCRIPT_SANDBOXING=YES

LOCALIZATION_PREFERS_STRING_CATALOGS=YES
MTL_FAST_MATH=YES

STRIP_INSTALLED_PRODUCT=YES
DEAD_CODE_STRIPPING=NO
SWIFT_TREAT_WARNINGS_AS_ERRORS=YES
OTHER_SWIFT_FLAGS[config=Debug]=-DDEBUG

COPY_PHASE_STRIP[config=Debug]=NO
ENABLE_NS_ASSERTIONS[config=Release]=NO
ENABLE_TESTABILITY[config=Debug]=YES
GCC_OPTIMIZATION_LEVEL[config=Debug]=0

ONLY_ACTIVE_ARCH[config=Debug]=YES
SWIFT_COMPILATION_MODE[config=Release]=wholemodule
SWIFT_OPTIMIZATION_LEVEL[config=Debug]=-Onone
VALIDATE_PRODUCT[config=Release]=YES

SWIFT_ENABLE_EXPLICIT_MODULES = YES
_EXPERIMENTAL_SWIFT_EXPLICIT_MODULES = YES
COMPILATION_CACHE_ENABLE_CACHING[config=Debug] = YES